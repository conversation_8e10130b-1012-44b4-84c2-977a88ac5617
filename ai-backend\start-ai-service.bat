@echo off
echo Starting AgroPulse AI Service...

REM Remove existing virtual environment if corrupted
if exist "venv" (
    echo Removing existing virtual environment...
    rmdir /s /q venv
)

REM Create new virtual environment
echo Creating virtual environment...
python -m venv venv

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip first
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install compatible versions
echo Installing compatible Python dependencies...
pip install flask==2.3.3 flask-cors==4.0.0 werkzeug==2.3.7

REM Start the AI service
echo Starting AI Service on http://localhost:5000...
python simple_ai_app.py

pause
