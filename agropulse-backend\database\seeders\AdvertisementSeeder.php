<?php

namespace Database\Seeders;

use App\Models\Advertisement;
use App\Models\User;
use Illuminate\Database\Seeder;

class AdvertisementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->info('No users found. Please run UserSeeder first.');
            return;
        }

        // Create advertisements
        $advertisements = [
            [
                'title' => 'Premium Egyptian Cotton',
                'description' => 'High-quality Egyptian cotton available for export. Known for its exceptional softness and durability.',
                'type' => 'banner',
                'image_url' => 'https://images.unsplash.com/photo-1591118988096-61a4c68acf5c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                'landing_url' => 'https://agropulse.com/products/cotton',
                'target_countries' => ['USA', 'Germany', 'Japan', 'UK'],
                'target_products' => ['Cotton', 'Textiles'],
                'target_user_roles' => ['importer', 'manufacturer'],
                'start_date' => now(),
                'end_date' => now()->addMonths(3),
                'budget' => 1500.00,
                'daily_budget' => 50.00,
                'is_featured' => true,
                'placement' => 'homepage',
                'status' => 'active',
                'impressions' => 45000,
                'clicks' => 1200,
                'conversions' => 35,
                'ctr' => 2.67,
                'conversion_rate' => 2.92,
            ],
            [
                'title' => 'Organic Avocados from Mexico',
                'description' => 'Fresh organic avocados from Mexican farms. Perfect ripeness and excellent quality guaranteed.',
                'image_url' => 'https://images.unsplash.com/photo-1519162808019-7de1683fa2ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                'target_countries' => ['USA', 'Canada', 'France', 'Germany'],
                'target_products' => ['Avocados', 'Fruits'],
                'start_date' => now(),
                'end_date' => now()->addMonths(2),
                'budget' => 1200.00,
                'is_featured' => true,
                'status' => 'approved',
            ],
            [
                'title' => 'Premium Coffee Beans PPC Campaign',
                'description' => 'Specialty coffee beans from Colombia. Rich flavor profile with notes of chocolate and caramel.',
                'type' => 'ppc',
                'image_url' => 'https://images.unsplash.com/photo-1559525839-b184a4d698c7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                'landing_url' => 'https://agropulse.com/products/coffee',
                'target_countries' => ['USA', 'UK', 'Germany', 'France', 'Italy'],
                'target_products' => ['Coffee', 'Beverages'],
                'target_user_roles' => ['coffee_roaster', 'distributor', 'cafe_owner'],
                'target_demographics' => ['coffee_enthusiasts', 'business_owners'],
                'start_date' => now(),
                'end_date' => now()->addMonths(3),
                'budget' => 2000.00,
                'daily_budget' => 65.00,
                'bid_amount' => 2.80,
                'keywords' => 'premium coffee, colombian coffee, specialty coffee, coffee beans',
                'ad_groups' => ['Premium Coffee', 'Colombian Coffee', 'Specialty Beans'],
                'is_featured' => false,
                'placement' => 'search_results',
                'status' => 'pending_approval',
                'impressions' => 0,
                'clicks' => 0,
                'conversions' => 0,
                'ctr' => 0,
                'conversion_rate' => 0,
                'cost_per_click' => 0,
                'cost_per_conversion' => 0,
                'quality_score' => 0,
                'ad_rank' => 0,
            ],
            [
                'title' => 'Fresh Citrus Fruits',
                'description' => 'Variety of fresh citrus fruits including oranges, lemons, and grapefruits. Direct from Spanish orchards.',
                'image_url' => 'https://images.unsplash.com/photo-1611080626919-7cf5a9dbab12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                'target_countries' => ['Germany', 'France', 'UK', 'Netherlands'],
                'target_products' => ['Citrus', 'Fruits'],
                'start_date' => now(),
                'end_date' => now()->addMonths(2),
                'budget' => 1800.00,
                'is_featured' => false,
                'status' => 'pending',
            ],
            [
                'title' => 'Premium Olive Oil',
                'description' => 'Extra virgin olive oil from Italian groves. Cold-pressed and bottled for maximum flavor and health benefits.',
                'image_url' => 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                'target_countries' => ['USA', 'Canada', 'UK', 'Germany', 'France'],
                'target_products' => ['Olive Oil', 'Cooking Oils'],
                'start_date' => now(),
                'end_date' => now()->addMonths(4),
                'budget' => 2500.00,
                'is_featured' => true,
                'status' => 'approved',
            ],
        ];

        foreach ($advertisements as $adData) {
            $user = $users->random();
            $advertisement = new Advertisement($adData);
            $advertisement->user_id = $user->id;

            // If approved, set approved_by and approved_at
            if ($adData['status'] === 'approved') {
                $moderator = $users->where('id', '!=', $user->id)->random();
                $advertisement->approved_by = $moderator->id;
                $advertisement->approved_at = now()->subDays(rand(1, 10));
            }

            $advertisement->save();
        }

        $this->command->info('Advertisements seeded successfully.');
    }
}
