import { backendService } from './api';
import axios from 'axios';
import { API_BASE_URL, USE_MOCK_DATA } from '../config/api';

// Create a dedicated API instance for marketing
const api = axios.create({
  baseURL: `${API_BASE_URL}`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

/**
 * Service for interacting with the marketing campaign API endpoints
 */
class MarketingService {
  /**
   * Get all marketing campaigns with optional filters
   * @param {Object} params - Query parameters
   * @returns {Promise} - API response
   */
  getCampaigns(params = {}) {
    if (USE_MOCK_DATA) {
      return this.getMockCampaigns(params);
    }
    return api.get('/marketing-campaigns', { params });
  }

  /**
   * Get a marketing campaign by ID
   * @param {number} id - Campaign ID
   * @returns {Promise} - API response
   */
  getCampaign(id) {
    return api.get(`/marketing-campaigns/${id}`);
  }

  /**
   * Create a new marketing campaign
   * @param {FormData} formData - Campaign data
   * @returns {Promise} - API response
   */
  createCampaign(formData) {
    return api.post('/marketing-campaigns', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Update an existing marketing campaign
   * @param {number} id - Campaign ID
   * @param {FormData} formData - Updated campaign data
   * @returns {Promise} - API response
   */
  updateCampaign(id, formData) {
    return api.post(`/marketing-campaigns/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-HTTP-Method-Override': 'PUT',
      },
    });
  }

  /**
   * Delete a marketing campaign
   * @param {number} id - Campaign ID
   * @returns {Promise} - API response
   */
  deleteCampaign(id) {
    return api.delete(`/marketing-campaigns/${id}`);
  }

  /**
   * Get campaign analytics
   * @param {number} id - Campaign ID
   * @param {Object} params - Query parameters
   * @returns {Promise} - API response
   */
  getCampaignAnalytics(id, params = {}) {
    return api.get(`/marketing-campaigns/${id}/analytics`, { params });
  }

  /**
   * Get all PPC advertisements with optional filters
   * @param {Object} params - Query parameters
   * @returns {Promise} - API response
   */
  getPPCAdvertisements(params = {}) {
    return api.get('/advertisements', {
      params: {
        ...params,
        type: 'ppc'
      }
    });
  }

  /**
   * Get a PPC advertisement by ID
   * @param {number} id - Advertisement ID
   * @returns {Promise} - API response
   */
  getPPCAdvertisement(id) {
    return api.get(`/advertisements/${id}`);
  }

  /**
   * Create a new PPC advertisement
   * @param {FormData} formData - Advertisement data
   * @returns {Promise} - API response
   */
  createPPCAdvertisement(formData) {
    if (USE_MOCK_DATA) {
      return this.createMockPPCAdvertisement(formData);
    }

    return api.post('/advertisements', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Update an existing PPC advertisement
   * @param {number} id - Advertisement ID
   * @param {FormData} formData - Updated advertisement data
   * @returns {Promise} - API response
   */
  updatePPCAdvertisement(id, formData) {
    return api.post(`/advertisements/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-HTTP-Method-Override': 'PUT',
      },
    });
  }

  /**
   * Delete a PPC advertisement
   * @param {number} id - Advertisement ID
   * @returns {Promise} - API response
   */
  deletePPCAdvertisement(id) {
    return api.delete(`/advertisements/${id}`);
  }

  /**
   * Get advertisement analytics
   * @param {number} id - Advertisement ID
   * @param {Object} params - Query parameters
   * @returns {Promise} - API response
   */
  getPPCAdvertisementAnalytics(id, params = {}) {
    return api.get(`/advertisements/${id}/analytics`, { params });
  }

  /**
   * Update an existing marketing campaign
   * @param {number} id - Campaign ID
   * @param {FormData} formData - Updated campaign data
   * @returns {Promise} - API response
   */
  updateCampaign(id, formData) {
    return api.post(`/marketing-campaigns/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-HTTP-Method-Override': 'PUT',
      },
    });
  }

  /**
   * Delete a marketing campaign
   * @param {number} id - Campaign ID
   * @returns {Promise} - API response
   */
  deleteCampaign(id) {
    return api.delete(`/marketing-campaigns/${id}`);
  }

  /**
   * Get campaign metrics
   * @param {number} id - Campaign ID
   * @param {Object} params - Query parameters
   * @returns {Promise} - API response
   */
  getCampaignMetrics(id, params = {}) {
    return api.get(`/marketing-campaigns/${id}/metrics`, { params });
  }

  /**
   * Update campaign metrics
   * @param {number} id - Campaign ID
   * @param {Object} metricsData - Metrics data
   * @returns {Promise} - API response
   */
  updateCampaignMetrics(id, metricsData) {
    return api.post(`/marketing-campaigns/${id}/metrics`, metricsData);
  }

  /**
   * Get active campaigns
   * @param {Object} params - Query parameters
   * @returns {Promise} - API response
   */
  getActiveCampaigns(params = {}) {
    return api.get('/marketing-campaigns', {
      params: {
        ...params,
        active: true,
        status: 'active'
      }
    });
  }

  /**
   * Get campaigns by type
   * @param {string} type - Campaign type
   * @param {Object} params - Additional query parameters
   * @returns {Promise} - API response
   */
  getCampaignsByType(type, params = {}) {
    return api.get('/marketing-campaigns', {
      params: {
        ...params,
        type
      }
    });
  }

  /**
   * Get campaigns by date range
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @param {Object} params - Additional query parameters
   * @returns {Promise} - API response
   */
  getCampaignsByDateRange(startDate, endDate, params = {}) {
    return api.get('/marketing-campaigns', {
      params: {
        ...params,
        start_date: startDate,
        end_date: endDate
      }
    });
  }

  /**
   * Get campaign performance summary
   * @param {number} id - Campaign ID
   * @returns {Promise} - API response
   */
  getCampaignPerformance(id) {
    return api.get(`/marketing-campaigns/${id}/metrics`, {
      params: {
        group_by: 'day'
      }
    });
  }

  /**
   * Get campaign ROI data
   * @param {number} id - Campaign ID
   * @returns {Promise} - API response
   */
  getCampaignROI(id) {
    return api.get(`/marketing-campaigns/${id}/metrics`, {
      params: {
        group_by: 'month'
      }
    });
  }

  /**
   * Mock function to create PPC advertisement
   * @param {FormData} formData - Advertisement data
   * @returns {Promise} - Mock response
   */
  createMockPPCAdvertisement(formData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Convert FormData to object for logging
        const data = {};
        for (let [key, value] of formData.entries()) {
          data[key] = value;
        }

        console.log('Mock PPC Advertisement created:', data);

        resolve({
          data: {
            advertisement: {
              id: Math.floor(Math.random() * 1000) + 1,
              title: data.title || 'New PPC Advertisement',
              description: data.description || 'Advertisement description',
              budget: parseFloat(data.budget) || 100,
              target_audience: data.target_audience || 'General',
              status: 'pending_approval',
              type: 'ppc',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          },
          message: 'PPC Advertisement created successfully and is pending approval'
        });
      }, 1000);
    });
  }

  /**
   * Mock function to get campaigns
   * @param {Object} params - Query parameters
   * @returns {Promise} - Mock response
   */
  getMockCampaigns(params = {}) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockCampaigns = [
          {
            id: 1,
            name: 'Summer Wheat Campaign',
            description: 'Promoting premium wheat products for summer season',
            budget: 5000,
            spent: 2500,
            status: 'active',
            type: 'seasonal',
            target_audience: 'Farmers and Distributors',
            start_date: '2024-06-01',
            end_date: '2024-08-31',
            created_at: '2024-05-15T00:00:00Z',
            metrics: {
              impressions: 125000,
              clicks: 3200,
              conversions: 85,
              ctr: 2.56,
              conversion_rate: 2.66
            }
          },
          {
            id: 2,
            name: 'Organic Produce Promotion',
            description: 'Marketing campaign for organic fruits and vegetables',
            budget: 3000,
            spent: 1800,
            status: 'active',
            type: 'product',
            target_audience: 'Health-conscious consumers',
            start_date: '2024-07-01',
            end_date: '2024-09-30',
            created_at: '2024-06-20T00:00:00Z',
            metrics: {
              impressions: 89000,
              clicks: 2100,
              conversions: 62,
              ctr: 2.36,
              conversion_rate: 2.95
            }
          },
          {
            id: 3,
            name: 'Export Market Expansion',
            description: 'Campaign to expand into new export markets',
            budget: 8000,
            spent: 4200,
            status: 'paused',
            type: 'expansion',
            target_audience: 'International buyers',
            start_date: '2024-05-01',
            end_date: '2024-12-31',
            created_at: '2024-04-25T00:00:00Z',
            metrics: {
              impressions: 156000,
              clicks: 4800,
              conversions: 120,
              ctr: 3.08,
              conversion_rate: 2.50
            }
          }
        ];

        resolve({
          data: {
            campaigns: mockCampaigns,
            total: mockCampaigns.length,
            current_page: 1,
            per_page: 10
          }
        });
      }, 800);
    });
  }
}

export default new MarketingService();
