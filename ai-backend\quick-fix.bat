@echo off
echo ========================================
echo    Quick Fix for Flask/Werkzeug Issue
echo ========================================

REM Make sure we're in the right directory
cd /d "%~dp0"

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Uninstall problematic packages
echo Uninstalling problematic packages...
pip uninstall -y flask werkzeug flask-cors

REM Install compatible versions
echo Installing compatible versions...
pip install flask==2.3.3 flask-cors==4.0.0 werkzeug==2.3.7

echo.
echo ========================================
echo Fix completed! Try running the AI service now.
echo ========================================
echo.
echo Run: py simple_ai_app.py
echo.
pause
