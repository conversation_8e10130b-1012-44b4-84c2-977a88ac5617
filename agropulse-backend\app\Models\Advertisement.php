<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Advertisement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'type',
        'image_url',
        'landing_url',
        'target_countries',
        'target_products',
        'target_user_roles',
        'target_demographics',
        'start_date',
        'end_date',
        'budget',
        'daily_budget',
        'bid_amount',
        'keywords',
        'ad_groups',
        'is_featured',
        'placement',
        'performance_metrics',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'target_countries' => 'array',
        'target_products' => 'array',
        'target_user_roles' => 'array',
        'target_demographics' => 'array',
        'ad_groups' => 'array',
        'performance_metrics' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
        'budget' => 'decimal:2',
        'daily_budget' => 'decimal:2',
        'bid_amount' => 'decimal:2',
        'cost_per_click' => 'decimal:4',
        'cost_per_conversion' => 'decimal:4',
        'quality_score' => 'decimal:1',
        'is_featured' => 'boolean',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    /**
     * Get the user that owns the advertisement.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the approver of the advertisement.
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the rejecter of the advertisement.
     */
    public function rejecter()
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }
}
