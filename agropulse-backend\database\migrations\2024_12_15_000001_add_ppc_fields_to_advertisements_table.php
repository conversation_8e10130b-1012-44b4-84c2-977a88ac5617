<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('advertisements', function (Blueprint $table) {
            // Add PPC specific fields
            $table->enum('type', ['banner', 'ppc', 'social', 'email'])->default('banner')->after('description');
            $table->string('landing_url')->nullable()->after('image_url');
            $table->decimal('daily_budget', 8, 2)->nullable()->after('budget');
            $table->decimal('bid_amount', 8, 2)->nullable()->after('daily_budget');
            $table->text('keywords')->nullable()->after('bid_amount');
            $table->json('ad_groups')->nullable()->after('keywords');
            $table->decimal('cost_per_click', 8, 4)->default(0)->after('conversion_rate');
            $table->decimal('cost_per_conversion', 8, 4)->default(0)->after('cost_per_click');
            $table->decimal('quality_score', 3, 1)->default(0)->after('cost_per_conversion');
            $table->integer('ad_rank')->default(0)->after('quality_score');
            
            // Update status enum to include more PPC-specific statuses
            $table->dropColumn('status');
        });
        
        // Add the new status column with updated enum values
        Schema::table('advertisements', function (Blueprint $table) {
            $table->enum('status', [
                'draft', 
                'pending', 
                'pending_approval', 
                'approved', 
                'active', 
                'paused', 
                'completed', 
                'rejected', 
                'cancelled'
            ])->default('draft')->after('ad_rank');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('advertisements', function (Blueprint $table) {
            // Remove PPC specific fields
            $table->dropColumn([
                'type',
                'landing_url',
                'daily_budget',
                'bid_amount',
                'keywords',
                'ad_groups',
                'cost_per_click',
                'cost_per_conversion',
                'quality_score',
                'ad_rank',
                'status'
            ]);
        });
        
        // Restore original status column
        Schema::table('advertisements', function (Blueprint $table) {
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
        });
    }
};
