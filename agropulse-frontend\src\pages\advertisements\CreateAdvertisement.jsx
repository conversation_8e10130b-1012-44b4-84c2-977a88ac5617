import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { toast } from 'react-toastify'
import { useAuth } from '../../contexts/AuthContext'
import AdvertisementService from '../../services/AdvertisementService'

// Icons
import {
  ArrowLeftIcon,
  PhotoIcon,
  GlobeAltIcon,
  ShoppingBagIcon,
  CalendarIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'

const CreateAdvertisement = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { user, isLoading: authLoading, isAuthenticated } = useAuth()

  // Redirect if not authenticated
  React.useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/login')
    }
  }, [authLoading, isAuthenticated, navigate])

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image_url: '',
    target_countries: [],
    target_products: [],
    start_date: '',
    end_date: '',
    budget: '',
    is_featured: false
  })

  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Available countries and products for selection
  const availableCountries = [
    'Egypt', 'USA', 'Germany', 'China', 'Russia', 'France', 'UK',
    'Italy', 'Spain', 'Japan', 'Brazil', 'India', 'Canada', 'Australia'
  ]

  const availableProducts = [
    'Apples', 'Oranges', 'Wheat', 'Corn', 'Rice', 'Coffee',
    'Tomatoes', 'Potatoes', 'Grapes', 'Bananas', 'Olives', 'Cotton'
  ]

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target

    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }))
    }
  }

  // Handle multi-select changes
  const handleMultiSelectChange = (e, field) => {
    const options = Array.from(e.target.selectedOptions).map(option => option.value)

    setFormData(prev => ({
      ...prev,
      [field]: options
    }))

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }))
    }
  }

  // Validate form
  const validateForm = () => {
    const newErrors = {}

    if (!formData.title.trim()) {
      newErrors.title = t('advertisements.validation.titleRequired')
    }

    if (!formData.description.trim()) {
      newErrors.description = t('advertisements.validation.descriptionRequired')
    }

    if (!formData.image_url.trim()) {
      newErrors.image_url = t('advertisements.validation.imageRequired')
    }

    if (formData.target_countries.length === 0) {
      newErrors.target_countries = t('advertisements.validation.countriesRequired')
    }

    if (formData.target_products.length === 0) {
      newErrors.target_products = t('advertisements.validation.productsRequired')
    }

    if (!formData.start_date) {
      newErrors.start_date = t('advertisements.validation.startDateRequired')
    }

    if (!formData.end_date) {
      newErrors.end_date = t('advertisements.validation.endDateRequired')
    } else if (formData.start_date && new Date(formData.end_date) <= new Date(formData.start_date)) {
      newErrors.end_date = t('advertisements.validation.endDateAfterStart')
    }

    if (!formData.budget) {
      newErrors.budget = t('advertisements.validation.budgetRequired')
    } else if (isNaN(formData.budget) || parseFloat(formData.budget) <= 0) {
      newErrors.budget = t('advertisements.validation.budgetPositive')
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      setIsSubmitting(true)

      // Check if user is available
      if (!user || !user.id) {
        toast.error('User not authenticated. Please log in again.')
        navigate('/login')
        return
      }

      // Prepare advertisement data
      const advertisementData = {
        ...formData,
        user_id: user.id,
        budget: parseFloat(formData.budget)
      }

      console.log('Submitting advertisement data:', advertisementData) // Debug log

      const response = await AdvertisementService.createAdvertisement(advertisementData)
      console.log('Create advertisement response:', response) // Debug log

      // Extract advertisement ID with fallbacks
      const advertisementId = response?.data?.advertisement?.id || response?.advertisement?.id || response?.id

      if (!advertisementId) {
        console.error('No advertisement ID found in response:', response)
        toast.error('Advertisement created but ID not found')
        navigate('/dashboard/advertisements')
        return
      }

      toast.success(t('advertisements.createSuccess'))
      navigate(`/dashboard/advertisements/${advertisementId}`)
    } catch (error) {
      console.error('Error creating advertisement:', error)

      // Handle validation errors from the server
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors)
      } else if (error.response?.data?.message) {
        toast.error(error.response.data.message)
      } else {
        toast.error(t('advertisements.createError'))
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Show loading while auth is loading or user is not available
  if (authLoading || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <div className="mb-6">
        <Link
          to="/dashboard/advertisements"
          className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-1" />
          {t('common.back')}
        </Link>
      </div>

      {/* Page Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {t('advertisements.create')}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          {t('advertisements.createDescription')}
        </p>
      </div>

      {/* Advertisement Form */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          {/* Basic Information */}
          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              {t('advertisements.basicInformation')}
            </h2>

            <div className="grid grid-cols-1 gap-6">
              {/* Title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                  {t('advertisements.title')} *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className={`mt-1 block w-full rounded-md shadow-sm sm:text-sm ${
                    errors.title ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                  }`}
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  {t('advertisements.description')} *
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  value={formData.description}
                  onChange={handleChange}
                  className={`mt-1 block w-full rounded-md shadow-sm sm:text-sm ${
                    errors.description ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                  }`}
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                )}
              </div>

              {/* Image URL */}
              <div>
                <label htmlFor="image_url" className="block text-sm font-medium text-gray-700">
                  {t('advertisements.imageUrl')} *
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                    <PhotoIcon className="h-5 w-5" />
                  </span>
                  <input
                    type="text"
                    id="image_url"
                    name="image_url"
                    value={formData.image_url}
                    onChange={handleChange}
                    placeholder="https://example.com/image.jpg"
                    className={`flex-1 block w-full rounded-none rounded-r-md sm:text-sm ${
                      errors.image_url ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                    }`}
                  />
                </div>
                {errors.image_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.image_url}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  {t('advertisements.imageUrlHelp')}
                </p>
              </div>
            </div>
          </div>

          {/* Targeting */}
          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              {t('advertisements.targeting')}
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Target Countries */}
              <div>
                <label htmlFor="target_countries" className="block text-sm font-medium text-gray-700">
                  {t('advertisements.targetCountries')} *
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                    <GlobeAltIcon className="h-5 w-5" />
                  </span>
                  <select
                    id="target_countries"
                    name="target_countries"
                    multiple
                    size={5}
                    onChange={(e) => handleMultiSelectChange(e, 'target_countries')}
                    className={`flex-1 block w-full rounded-none rounded-r-md sm:text-sm ${
                      errors.target_countries ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                    }`}
                  >
                    {availableCountries.map(country => (
                      <option key={country} value={country}>
                        {country}
                      </option>
                    ))}
                  </select>
                </div>
                {errors.target_countries && (
                  <p className="mt-1 text-sm text-red-600">{errors.target_countries}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  {t('advertisements.multiSelectHelp')}
                </p>
              </div>

              {/* Target Products */}
              <div>
                <label htmlFor="target_products" className="block text-sm font-medium text-gray-700">
                  {t('advertisements.targetProducts')} *
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                    <ShoppingBagIcon className="h-5 w-5" />
                  </span>
                  <select
                    id="target_products"
                    name="target_products"
                    multiple
                    size={5}
                    onChange={(e) => handleMultiSelectChange(e, 'target_products')}
                    className={`flex-1 block w-full rounded-none rounded-r-md sm:text-sm ${
                      errors.target_products ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                    }`}
                  >
                    {availableProducts.map(product => (
                      <option key={product} value={product}>
                        {product}
                      </option>
                    ))}
                  </select>
                </div>
                {errors.target_products && (
                  <p className="mt-1 text-sm text-red-600">{errors.target_products}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  {t('advertisements.multiSelectHelp')}
                </p>
              </div>
            </div>
          </div>

          {/* Schedule and Budget */}
          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              {t('advertisements.scheduleAndBudget')}
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Start Date */}
              <div>
                <label htmlFor="start_date" className="block text-sm font-medium text-gray-700">
                  {t('advertisements.startDate')} *
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                    <CalendarIcon className="h-5 w-5" />
                  </span>
                  <input
                    type="date"
                    id="start_date"
                    name="start_date"
                    value={formData.start_date}
                    onChange={handleChange}
                    min={new Date().toISOString().split('T')[0]}
                    className={`flex-1 block w-full rounded-none rounded-r-md sm:text-sm ${
                      errors.start_date ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                    }`}
                  />
                </div>
                {errors.start_date && (
                  <p className="mt-1 text-sm text-red-600">{errors.start_date}</p>
                )}
              </div>

              {/* End Date */}
              <div>
                <label htmlFor="end_date" className="block text-sm font-medium text-gray-700">
                  {t('advertisements.endDate')} *
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                    <CalendarIcon className="h-5 w-5" />
                  </span>
                  <input
                    type="date"
                    id="end_date"
                    name="end_date"
                    value={formData.end_date}
                    onChange={handleChange}
                    min={formData.start_date || new Date().toISOString().split('T')[0]}
                    className={`flex-1 block w-full rounded-none rounded-r-md sm:text-sm ${
                      errors.end_date ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                    }`}
                  />
                </div>
                {errors.end_date && (
                  <p className="mt-1 text-sm text-red-600">{errors.end_date}</p>
                )}
              </div>

              {/* Budget */}
              <div>
                <label htmlFor="budget" className="block text-sm font-medium text-gray-700">
                  {t('advertisements.budget')} *
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                    <CurrencyDollarIcon className="h-5 w-5" />
                  </span>
                  <input
                    type="number"
                    id="budget"
                    name="budget"
                    value={formData.budget}
                    onChange={handleChange}
                    min="1"
                    step="0.01"
                    placeholder="500"
                    className={`flex-1 block w-full rounded-none rounded-r-md sm:text-sm ${
                      errors.budget ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                    }`}
                  />
                </div>
                {errors.budget && (
                  <p className="mt-1 text-sm text-red-600">{errors.budget}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  {t('advertisements.budgetHelp')}
                </p>
              </div>

              {/* Featured */}
              <div className="flex items-center">
                <input
                  id="is_featured"
                  name="is_featured"
                  type="checkbox"
                  checked={formData.is_featured}
                  onChange={handleChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-900">
                  {t('advertisements.featured')}
                </label>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3">
            <Link
              to="/dashboard/advertisements"
              className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              {t('common.cancel')}
            </Link>
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? t('common.submitting') : t('common.create')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default CreateAdvertisement
