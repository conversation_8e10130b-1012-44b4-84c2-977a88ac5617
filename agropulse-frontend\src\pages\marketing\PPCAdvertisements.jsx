import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import MarketingService from '../../services/MarketingService';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import EmptyState from '../../components/ui/EmptyState';
import { formatDate, formatCurrency } from '../../utils/formatters';

// Icons
import {
  PlusIcon,
  ArrowPathIcon,
  ChartBarIcon,
  PencilSquareIcon,
  TrashIcon,
  FunnelIcon,
  ChevronDownIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  CursorArrowRaysIcon,
  ShoppingCartIcon,
  GlobeAltIcon,
  TagIcon,
  PhotoIcon,
} from '@heroicons/react/24/outline';

const PPCAdvertisements = () => {
  const { t } = useTranslation();

  const [advertisements, setAdvertisements] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [adToDelete, setAdToDelete] = useState(null);

  // Filters
  const [filters, setFilters] = useState({
    status: '',
    country: '',
    product: '',
  });

  // Sort
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');

  useEffect(() => {
    fetchAdvertisements();
  }, [filters, sortField, sortDirection]);

  const fetchAdvertisements = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = {
        ...filters,
        sort_by: sortField,
        sort_direction: sortDirection,
        type: 'ppc',
      };

      console.log('Fetching PPC advertisements with params:', params);
      const response = await MarketingService.getPPCAdvertisements(params);
      console.log('PPC advertisements response:', response);

      // Handle different response structures
      const adsData = response?.data?.data || response?.data?.advertisements || response?.data || [];
      setAdvertisements(Array.isArray(adsData) ? adsData : []);
    } catch (error) {
      console.error('Error fetching PPC advertisements:', error);
      setError(error.message || t('marketing.ppc.fetchError'));
      // Set empty array on error to prevent crashes
      setAdvertisements([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleSortChange = (field) => {
    if (field === sortField) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleDeleteClick = (ad) => {
    setAdToDelete(ad);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!adToDelete) return;

    try {
      await MarketingService.deletePPCAdvertisement(adToDelete.id);
      toast.success(t('marketing.ppc.deleteSuccess'));

      // Remove the deleted ad from the list
      setAdvertisements(advertisements.filter(ad => ad.id !== adToDelete.id));

      // Close the modal
      setShowDeleteModal(false);
      setAdToDelete(null);
    } catch (error) {
      console.error('Error deleting PPC advertisement:', error);
      toast.error(error.response?.data?.message || t('marketing.ppc.deleteError'));
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {t('marketing.ppc.statusActive')}
          </span>
        );
      case 'paused':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            {t('marketing.ppc.statusPaused')}
          </span>
        );
      case 'completed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {t('marketing.ppc.statusCompleted')}
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            {t('marketing.ppc.statusPending')}
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            {t('marketing.ppc.statusRejected')}
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">{t('marketing.ppc.title')}</h1>
        <div className="flex space-x-2">
          <button
            onClick={fetchAdvertisements}
            className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <ArrowPathIcon className="h-4 w-4 mr-1" />
            {t('common.refresh')}
          </button>
          <Link
            to="/dashboard/marketing/ppc/create"
            className="flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            {t('marketing.ppc.create')}
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:space-x-4">
          <div className="flex items-center mb-4 md:mb-0">
            <FunnelIcon className="h-5 w-5 text-gray-400 mr-2" />
            <span className="text-sm font-medium text-gray-700">{t('common.filter')}:</span>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.status')}
              </label>
              <select
                id="status"
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
              >
                <option value="">{t('common.all')}</option>
                <option value="active">{t('marketing.ppc.statusActive')}</option>
                <option value="paused">{t('marketing.ppc.statusPaused')}</option>
                <option value="completed">{t('marketing.ppc.statusCompleted')}</option>
                <option value="pending">{t('marketing.ppc.statusPending')}</option>
                <option value="rejected">{t('marketing.ppc.statusRejected')}</option>
              </select>
            </div>

            <div>
              <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.country')}
              </label>
              <select
                id="country"
                name="country"
                value={filters.country}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
              >
                <option value="">{t('common.all')}</option>
                <option value="US">United States</option>
                <option value="UK">United Kingdom</option>
                <option value="DE">Germany</option>
                <option value="FR">France</option>
                <option value="IT">Italy</option>
                <option value="ES">Spain</option>
                <option value="CA">Canada</option>
                <option value="AU">Australia</option>
                <option value="JP">Japan</option>
                <option value="CN">China</option>
              </select>
            </div>

            <div>
              <label htmlFor="product" className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.product')}
              </label>
              <select
                id="product"
                name="product"
                value={filters.product}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
              >
                <option value="">{t('common.all')}</option>
                <option value="wheat">Wheat</option>
                <option value="rice">Rice</option>
                <option value="corn">Corn</option>
                <option value="soybeans">Soybeans</option>
                <option value="coffee">Coffee</option>
                <option value="cotton">Cotton</option>
                <option value="sugar">Sugar</option>
                <option value="fruits">Fruits</option>
                <option value="vegetables">Vegetables</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {advertisements.length === 0 ? (
        <EmptyState
          icon={<CurrencyDollarIcon className="h-12 w-12 text-gray-400" />}
          title={t('marketing.ppc.noAds')}
          description={t('marketing.ppc.noAdsDesc')}
          actionText={t('marketing.ppc.create')}
          actionLink="/dashboard/marketing/ppc/create"
        />
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSortChange('title')}
                  >
                    {t('marketing.ppc.adTitle')}
                    {sortField === 'title' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSortChange('status')}
                  >
                    {t('common.status')}
                    {sortField === 'status' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSortChange('start_date')}
                  >
                    {t('marketing.ppc.dateRange')}
                    {sortField === 'start_date' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSortChange('budget')}
                  >
                    {t('marketing.ppc.budget')}
                    {sortField === 'budget' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {t('marketing.ppc.performance')}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {t('common.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {advertisements.map((ad) => (
                  <tr key={ad.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-md overflow-hidden">
                          {ad.image_url ? (
                            <img src={ad.image_url} alt={ad.title} className="h-10 w-10 object-cover" />
                          ) : (
                            <div className="h-10 w-10 bg-gray-200 flex items-center justify-center">
                              <PhotoIcon className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {ad.title}
                          </div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {ad.description}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(ad.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDate(ad.start_date)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {t('common.to')} {formatDate(ad.end_date)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatCurrency(ad.budget)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {t('marketing.ppc.spent')}: {formatCurrency(ad.spent_amount)}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${Math.min(100, (ad.spent_amount / ad.budget) * 100)}%` }}
                        ></div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <div className="text-sm text-gray-900 flex items-center">
                          <EyeIcon className="h-4 w-4 text-gray-400 mr-1" />
                          {ad.impressions.toLocaleString()}
                        </div>
                        <div className="text-sm text-gray-900 flex items-center">
                          <CursorArrowRaysIcon className="h-4 w-4 text-gray-400 mr-1" />
                          {ad.clicks.toLocaleString()} ({ad.ctr.toFixed(2)}%)
                        </div>
                        <div className="text-sm text-gray-900 flex items-center">
                          <ShoppingCartIcon className="h-4 w-4 text-gray-400 mr-1" />
                          {ad.conversions.toLocaleString()} ({ad.conversion_rate.toFixed(2)}%)
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Link
                          to={`/dashboard/marketing/ppc/${ad.id}/analytics`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <ChartBarIcon className="h-5 w-5" />
                        </Link>
                        <Link
                          to={`/dashboard/marketing/ppc/${ad.id}/edit`}
                          className="text-green-600 hover:text-green-900"
                        >
                          <PencilSquareIcon className="h-5 w-5" />
                        </Link>
                        <button
                          onClick={() => handleDeleteClick(ad)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && adToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mr-2" />
                <h2 className="text-xl font-semibold text-gray-900">{t('marketing.ppc.deleteConfirmTitle')}</h2>
              </div>
              <p className="mb-4 text-gray-600">
                {t('marketing.ppc.deleteConfirmMessage', { adTitle: adToDelete.title })}
              </p>
              <p className="mb-6 text-sm text-red-600">
                {t('marketing.ppc.deleteWarning')}
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowDeleteModal(false);
                    setAdToDelete(null);
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  {t('common.cancel')}
                </button>
                <button
                  onClick={handleDeleteConfirm}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  {t('marketing.ppc.confirmDelete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PPCAdvertisements;
