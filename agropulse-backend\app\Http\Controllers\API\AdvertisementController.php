<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Services\AdvertisementTargetingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Facades\CloudStorage;
use App\Facades\ImageOptimization;

class AdvertisementController extends Controller
{
    /**
     * Advertisement targeting service
     *
     * @var AdvertisementTargetingService
     */
    protected $targetingService;

    /**
     * Create a new controller instance.
     *
     * @param AdvertisementTargetingService $targetingService
     * @return void
     */
    public function __construct(AdvertisementTargetingService $targetingService)
    {
        $this->targetingService = $targetingService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Advertisement::with('user');

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('country')) {
            $query->whereJsonContains('target_countries', $request->country);
        }

        if ($request->has('product')) {
            $query->whereJsonContains('target_products', $request->product);
        }

        if ($request->has('featured') && $request->featured === 'true') {
            $query->where('is_featured', true);
        }

        // Get user's advertisements or all for moderators/admins
        $user = Auth::user();
        $profile = $user->profile;

        if ($profile && ($profile->role === 'moderator' || $profile->role === 'admin')) {
            // Moderators and admins can see all advertisements
        } else {
            // Regular users can only see their own advertisements
            $query->where('user_id', $user->id);
        }

        $advertisements = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json([
            'status' => 'success',
            'data' => $advertisements
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|string|in:banner,ppc,social,email',
            'image' => 'nullable|image|max:2048',
            'landing_url' => 'nullable|url',
            'target_countries' => 'required|array',
            'target_products' => 'required|array',
            'target_user_roles' => 'nullable|array',
            'target_demographics' => 'nullable|array',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'budget' => 'required|numeric|min:0',
            'daily_budget' => 'nullable|numeric|min:0',
            'bid_amount' => 'nullable|numeric|min:0',
            'keywords' => 'nullable|string',
            'ad_groups' => 'nullable|array',
            'is_featured' => 'boolean',
            'placement' => 'nullable|string|in:homepage,search_results,product_page,multiple'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $advertisement = new Advertisement($request->all());
        $advertisement->user_id = Auth::id();
        $advertisement->status = 'pending'; // All advertisements start as pending

        // Handle image upload
        if ($request->hasFile('image')) {
            // Optimize the image
            $optimizedImage = ImageOptimization::optimize($request->file('image'));

            // Store in cloud storage
            $imageUrl = CloudStorage::store($optimizedImage, 'advertisements');

            if ($imageUrl) {
                $advertisement->image_url = $imageUrl;
            }
        }

        $advertisement->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Advertisement created successfully',
            'data' => $advertisement
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $advertisement = Advertisement::with('user')->findOrFail($id);

        // Check if user can view this advertisement
        $user = Auth::user();
        $profile = $user->profile;

        if ($advertisement->user_id !== $user->id &&
            !($profile && ($profile->role === 'moderator' || $profile->role === 'admin'))) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized'
            ], 403);
        }

        return response()->json([
            'status' => 'success',
            'data' => $advertisement
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $advertisement = Advertisement::findOrFail($id);

        // Check if user can update this advertisement
        $user = Auth::user();
        if ($advertisement->user_id !== $user->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized'
            ], 403);
        }

        // Only allow editing if advertisement is in pending or rejected status
        if (!in_array($advertisement->status, ['pending', 'rejected'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Advertisement can only be edited when in pending or rejected status'
            ], 422);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'string|max:255',
            'description' => 'string',
            'type' => 'string|in:banner,ppc,social,email',
            'image' => 'nullable|image|max:2048',
            'landing_url' => 'nullable|url',
            'target_countries' => 'array',
            'target_products' => 'array',
            'target_user_roles' => 'nullable|array',
            'target_demographics' => 'nullable|array',
            'start_date' => 'date',
            'end_date' => 'date|after:start_date',
            'budget' => 'numeric|min:0',
            'daily_budget' => 'nullable|numeric|min:0',
            'bid_amount' => 'nullable|numeric|min:0',
            'keywords' => 'nullable|string',
            'ad_groups' => 'nullable|array',
            'is_featured' => 'boolean',
            'placement' => 'nullable|string|in:homepage,search_results,product_page,multiple'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Update advertisement fields
        $advertisement->fill($request->except(['image']));

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($advertisement->image_url) {
                CloudStorage::delete($advertisement->image_url);
            }

            // Optimize the image
            $optimizedImage = ImageOptimization::optimize($request->file('image'));

            // Store in cloud storage
            $imageUrl = CloudStorage::store($optimizedImage, 'advertisements');

            if ($imageUrl) {
                $advertisement->image_url = $imageUrl;
            }
        }

        // Reset status to pending if it was rejected
        if ($advertisement->status === 'rejected') {
            $advertisement->status = 'pending';
        }

        $advertisement->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Advertisement updated successfully',
            'data' => $advertisement
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $advertisement = Advertisement::findOrFail($id);

        // Check if user can delete this advertisement
        $user = Auth::user();
        $profile = $user->profile;

        if ($advertisement->user_id !== $user->id &&
            !($profile && ($profile->role === 'moderator' || $profile->role === 'admin'))) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized'
            ], 403);
        }

        // Delete image if exists
        if ($advertisement->image_url) {
            $path = str_replace('/storage', 'public', $advertisement->image_url);
            Storage::delete($path);
        }

        $advertisement->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Advertisement deleted successfully'
        ]);
    }

    /**
     * Get featured advertisements
     */
    public function featured()
    {
        $advertisements = Advertisement::where('status', 'approved')
            ->where('is_featured', true)
            ->whereDate('start_date', '<=', now())
            ->whereDate('end_date', '>=', now())
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $advertisements
        ]);
    }

    /**
     * Approve advertisement (for moderators/admins)
     */
    public function approve(string $id)
    {
        $user = Auth::user();
        $profile = $user->profile;

        // Check if user is moderator or admin
        if (!$profile || !in_array($profile->role, ['moderator', 'admin'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized'
            ], 403);
        }

        $advertisement = Advertisement::findOrFail($id);

        // Only allow approving if advertisement is in pending status
        if ($advertisement->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Advertisement can only be approved when in pending status'
            ], 422);
        }

        $advertisement->status = 'approved';
        $advertisement->approved_by = $user->id;
        $advertisement->approved_at = now();
        $advertisement->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Advertisement approved successfully',
            'data' => $advertisement
        ]);
    }

    /**
     * Reject advertisement (for moderators/admins)
     */
    public function reject(Request $request, string $id)
    {
        $user = Auth::user();
        $profile = $user->profile;

        // Check if user is moderator or admin
        if (!$profile || !in_array($profile->role, ['moderator', 'admin'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $advertisement = Advertisement::findOrFail($id);

        // Only allow rejecting if advertisement is in pending status
        if ($advertisement->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Advertisement can only be rejected when in pending status'
            ], 422);
        }

        $advertisement->status = 'rejected';
        $advertisement->rejection_reason = $request->reason;
        $advertisement->rejected_by = $user->id;
        $advertisement->rejected_at = now();
        $advertisement->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Advertisement rejected successfully',
            'data' => $advertisement
        ]);
    }

    /**
     * Get advertisements for moderator review
     */
    public function moderatorReview()
    {
        $user = Auth::user();
        $profile = $user->profile;

        // Check if user is moderator or admin
        if (!$profile || !in_array($profile->role, ['moderator', 'admin'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized'
            ], 403);
        }

        $advertisements = Advertisement::with('user')
            ->where('status', 'pending')
            ->orderBy('created_at', 'asc')
            ->paginate(10);

        return response()->json([
            'status' => 'success',
            'data' => $advertisements
        ]);
    }

    /**
     * Get personalized advertisements for the current user
     */
    public function personalized(Request $request)
    {
        $user = Auth::user();
        $limit = $request->limit ?? 5;

        // Get personalized advertisements
        $advertisements = $this->targetingService->getTargetedAdvertisements($user, $limit);

        // Track impressions (in a real implementation, this would be more sophisticated)
        foreach ($advertisements as $ad) {
            $ad->impressions += 1;
            $ad->save();
        }

        return response()->json([
            'status' => 'success',
            'data' => $advertisements
        ]);
    }

    /**
     * Track advertisement click
     */
    public function trackClick(string $id)
    {
        $advertisement = Advertisement::findOrFail($id);

        // Increment clicks
        $advertisement->clicks += 1;

        // Calculate CTR (Click-Through Rate)
        if ($advertisement->impressions > 0) {
            $advertisement->ctr = ($advertisement->clicks / $advertisement->impressions) * 100;
        }

        $advertisement->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Click tracked successfully'
        ]);
    }

    /**
     * Get advertisements related to a specific product or category
     */
    public function relatedToProduct(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required_without:category',
            'category' => 'required_without:product_id',
            'limit' => 'integer|min:1|max:10'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $limit = $request->limit ?? 3;
        $query = Advertisement::where('status', 'approved')
            ->whereDate('start_date', '<=', now())
            ->whereDate('end_date', '>=', now());

        // Filter by product or category
        if ($request->has('product_id')) {
            // In a real implementation, this would get the product and its category
            $productCategory = 'Sample Category'; // Placeholder
            $query->whereJsonContains('target_products', $productCategory);
        } elseif ($request->has('category')) {
            $query->whereJsonContains('target_products', $request->category);
        }

        $advertisements = $query->inRandomOrder()->limit($limit)->get();

        // Track impressions
        foreach ($advertisements as $ad) {
            $ad->impressions += 1;
            $ad->save();
        }

        return response()->json([
            'status' => 'success',
            'data' => $advertisements
        ]);
    }
}
