@echo off
echo ========================================
echo    Fixing AgroPulse AI Service
echo ========================================

REM Change to AI backend directory
cd /d "%~dp0"

REM Remove corrupted virtual environment
if exist "venv" (
    echo Removing corrupted virtual environment...
    rmdir /s /q venv
    echo Virtual environment removed.
)

REM Create fresh virtual environment
echo Creating fresh virtual environment...
python -m venv venv
if %errorlevel% neq 0 (
    echo ERROR: Failed to create virtual environment. Make sure Python is installed.
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ERROR: Failed to activate virtual environment.
    pause
    exit /b 1
)

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install minimal dependencies
echo Installing Flask and Flask-CORS...
pip install flask==2.2.3 flask-cors==3.0.10
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies.
    pause
    exit /b 1
)

echo.
echo ========================================
echo AI Service setup completed successfully!
echo ========================================
echo.
echo You can now start the AI service by running:
echo python simple_ai_app.py
echo.
echo Or use the start-ai-service.bat script.
echo.
pause
