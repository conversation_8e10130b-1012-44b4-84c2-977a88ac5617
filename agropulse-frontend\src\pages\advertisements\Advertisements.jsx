import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import AdvertisementService from '../../services/AdvertisementService'

// Icons
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  CheckIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

// Components
import AdvertisementCard from '../../components/advertisements/AdvertisementCard'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

const Advertisements = () => {
  const { t } = useTranslation()
  const [advertisements, setAdvertisements] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState({
    status: '',
    country: '',
    product: ''
  })
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch advertisements on component mount
  useEffect(() => {
    fetchAdvertisements()
  }, [])

  // Fetch advertisements with current filters
  const fetchAdvertisements = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await AdvertisementService.getAdvertisements(filters)
      console.log('Advertisement API Response:', response) // Debug log

      // Ensure we always have an array, even if the API response is unexpected
      const advertisementsData = response?.data?.advertisements || response?.advertisements || []
      console.log('Extracted advertisements data:', advertisementsData) // Debug log

      setAdvertisements(Array.isArray(advertisementsData) ? advertisementsData : [])
    } catch (error) {
      console.error('Error fetching advertisements:', error)
      setError(t('advertisements.fetchError'))
      // Set empty array on error to prevent filter issues
      setAdvertisements([])
    } finally {
      setIsLoading(false)
    }
  }

  // Apply filters
  const applyFilters = () => {
    fetchAdvertisements()
  }

  // Reset filters
  const resetFilters = () => {
    setFilters({
      status: '',
      country: '',
      product: ''
    })
    setSearchTerm('')
    fetchAdvertisements()
  }

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Filter advertisements by search term with additional safety checks
  const filteredAdvertisements = React.useMemo(() => {
    if (!Array.isArray(advertisements)) {
      return []
    }

    if (!searchTerm.trim()) {
      return advertisements
    }

    return advertisements.filter(ad => {
      if (!ad || typeof ad !== 'object') return false

      const title = ad.title || ''
      const description = ad.description || ''
      const searchLower = searchTerm.toLowerCase()

      return title.toLowerCase().includes(searchLower) ||
             description.toLowerCase().includes(searchLower)
    })
  }, [advertisements, searchTerm])

  // Get status badge
  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckIcon className="h-3 w-3 mr-1" />
            {t('advertisements.status.active')}
          </span>
        )
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="h-3 w-3 mr-1" />
            {t('advertisements.status.pending')}
          </span>
        )
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XMarkIcon className="h-3 w-3 mr-1" />
            {t('advertisements.status.rejected')}
          </span>
        )
      default:
        return null
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {t('advertisements.title')}
        </h1>

        <Link
          to="/dashboard/advertisements/create"
          className="flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          {t('advertisements.create')}
        </Link>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                placeholder={t('advertisements.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Filter Toggle Button */}
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            onClick={() => setShowFilters(!showFilters)}
          >
            <FunnelIcon className="h-5 w-5 mr-2 text-gray-500" />
            {t('common.filters')}
          </button>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Status Filter */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                {t('advertisements.filters.status')}
              </label>
              <select
                id="status"
                name="status"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm rounded-md"
                value={filters.status}
                onChange={handleFilterChange}
              >
                <option value="">{t('common.all')}</option>
                <option value="active">{t('advertisements.status.active')}</option>
                <option value="pending">{t('advertisements.status.pending')}</option>
                <option value="rejected">{t('advertisements.status.rejected')}</option>
              </select>
            </div>

            {/* Country Filter */}
            <div>
              <label htmlFor="country" className="block text-sm font-medium text-gray-700">
                {t('advertisements.filters.country')}
              </label>
              <select
                id="country"
                name="country"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm rounded-md"
                value={filters.country}
                onChange={handleFilterChange}
              >
                <option value="">{t('common.all')}</option>
                <option value="Egypt">Egypt</option>
                <option value="USA">USA</option>
                <option value="Germany">Germany</option>
                <option value="China">China</option>
                <option value="Russia">Russia</option>
                <option value="France">France</option>
                <option value="UK">UK</option>
                <option value="Italy">Italy</option>
                <option value="Spain">Spain</option>
                <option value="Japan">Japan</option>
              </select>
            </div>

            {/* Product Filter */}
            <div>
              <label htmlFor="product" className="block text-sm font-medium text-gray-700">
                {t('advertisements.filters.product')}
              </label>
              <select
                id="product"
                name="product"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm rounded-md"
                value={filters.product}
                onChange={handleFilterChange}
              >
                <option value="">{t('common.all')}</option>
                <option value="Apples">Apples</option>
                <option value="Oranges">Oranges</option>
                <option value="Wheat">Wheat</option>
                <option value="Corn">Corn</option>
                <option value="Rice">Rice</option>
                <option value="Coffee">Coffee</option>
                <option value="Tomatoes">Tomatoes</option>
                <option value="Potatoes">Potatoes</option>
              </select>
            </div>

            {/* Filter Action Buttons */}
            <div className="md:col-span-3 flex justify-end space-x-3">
              <button
                type="button"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                onClick={resetFilters}
              >
                {t('common.reset')}
              </button>
              <button
                type="button"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                onClick={applyFilters}
              >
                {t('common.apply')}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Advertisements List */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner />
        </div>
      ) : error ? (
        <div className="bg-red-50 p-4 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <XMarkIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">{error}</h3>
            </div>
          </div>
        </div>
      ) : filteredAdvertisements.length === 0 ? (
        <div className="bg-white p-8 rounded-lg shadow-sm text-center">
          <p className="text-gray-500">{t('advertisements.noAdvertisements')}</p>
          <Link
            to="/dashboard/advertisements/create"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            {t('advertisements.create')}
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAdvertisements.map((advertisement) => (
            <AdvertisementCard
              key={advertisement.id}
              advertisement={advertisement}
              statusBadge={getStatusBadge(advertisement.status)}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default Advertisements
